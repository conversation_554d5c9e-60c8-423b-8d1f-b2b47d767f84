#include <cstdio>
#include <string>
#include <algorithm>
#include <math.h>
#include <stdio.h>
#include <vector>

#include <cuda.h>
#include <cuda_runtime.h>
#include <driver_functions.h>
#include <thrust/device_ptr.h>
#include <thrust/for_each.h>
#include <thrust/iterator/counting_iterator.h>
#include <thrust/execution_policy.h>
#include <thrust/host_vector.h>
#include <thrust/device_vector.h>

// NVTX for profiling instrumentation
#include <nvtx3/nvToolsExt.h>

#include "cudaRenderer.h"
#include "image.h"
#include "noise.h"
#include "sceneLoader.h"
#include "util.h"

////////////////////////////////////////////////////////////////////////////////////////
// Putting all the cuda kernels here
///////////////////////////////////////////////////////////////////////////////////////

struct GlobalConstants {

    SceneName sceneName;

    int numCircles;
    float* position;
    float* velocity;
    float* color;
    float* radius;

    int imageWidth;
    int imageHeight;
    float* imageData;
};

// Global variable that is in scope, but read-only, for all cuda
// kernels.  The __constant__ modifier designates this variable will
// be stored in special "constant" memory on the GPU. (we didn't talk
// about this type of memory in class, but constant memory is a fast
// place to put read-only variables).
__constant__ GlobalConstants cuConstRendererParams;

// read-only lookup tables used to quickly compute noise (needed by
// advanceAnimation for the snowflake scene)
__constant__ int    cuConstNoiseYPermutationTable[256];
__constant__ int    cuConstNoiseXPermutationTable[256];
__constant__ float  cuConstNoise1DValueTable[256];

// color ramp table needed for the color ramp lookup shader
#define COLOR_MAP_SIZE 5
__constant__ float  cuConstColorRamp[COLOR_MAP_SIZE][3];


// including parts of the CUDA code from external files to keep this
// file simpler and to seperate code that should not be modified
#include "noiseCuda.cu_inl"
#include "lookupColor.cu_inl"


// kernelClearImageSnowflake -- (CUDA device code)
//
// Clear the image, setting the image to the white-gray gradation that
// is used in the snowflake image
__global__ void kernelClearImageSnowflake() {

    int imageX = blockIdx.x * blockDim.x + threadIdx.x;
    int imageY = blockIdx.y * blockDim.y + threadIdx.y;

    int width = cuConstRendererParams.imageWidth;
    int height = cuConstRendererParams.imageHeight;

    if (imageX >= width || imageY >= height)
        return;

    int offset = 4 * (imageY * width + imageX);
    float shade = .4f + .45f * static_cast<float>(height-imageY) / height;
    float4 value = make_float4(shade, shade, shade, 1.f);

    // write to global memory: As an optimization, I use a float4
    // store, that results in more efficient code than if I coded this
    // up as four seperate fp32 stores.
    *(float4*)(&cuConstRendererParams.imageData[offset]) = value;
}

// kernelClearImage --  (CUDA device code)
//
// Clear the image, setting all pixels to the specified color rgba
__global__ void kernelClearImage(float r, float g, float b, float a) {

    int imageX = blockIdx.x * blockDim.x + threadIdx.x;
    int imageY = blockIdx.y * blockDim.y + threadIdx.y;

    int width = cuConstRendererParams.imageWidth;
    int height = cuConstRendererParams.imageHeight;

    if (imageX >= width || imageY >= height)
        return;

    int offset = 4 * (imageY * width + imageX);
    float4 value = make_float4(r, g, b, a);

    // write to global memory: As an optimization, I use a float4
    // store, that results in more efficient code than if I coded this
    // up as four seperate fp32 stores.
    *(float4*)(&cuConstRendererParams.imageData[offset]) = value;
}

// kernelAdvanceFireWorks
// 
// Update the position of the fireworks (if circle is firework)
__global__ void kernelAdvanceFireWorks() {
    const float dt = 1.f / 60.f;
    const float pi = 3.14159;
    const float maxDist = 0.25f;

    float* velocity = cuConstRendererParams.velocity;
    float* position = cuConstRendererParams.position;
    float* radius = cuConstRendererParams.radius;

    int index = blockIdx.x * blockDim.x + threadIdx.x;
    if (index >= cuConstRendererParams.numCircles)
        return;

    if (0 <= index && index < NUM_FIREWORKS) { // firework center; no update 
        return;
    }

    // determine the fire-work center/spark indices
    int fIdx = (index - NUM_FIREWORKS) / NUM_SPARKS;
    int sfIdx = (index - NUM_FIREWORKS) % NUM_SPARKS;

    int index3i = 3 * fIdx;
    int sIdx = NUM_FIREWORKS + fIdx * NUM_SPARKS + sfIdx;
    int index3j = 3 * sIdx;

    float cx = position[index3i];
    float cy = position[index3i+1];

    // update position
    position[index3j] += velocity[index3j] * dt;
    position[index3j+1] += velocity[index3j+1] * dt;

    // fire-work sparks
    float sx = position[index3j];
    float sy = position[index3j+1];

    // compute vector from firework-spark
    float cxsx = sx - cx;
    float cysy = sy - cy;

    // compute distance from fire-work 
    float dist = sqrt(cxsx * cxsx + cysy * cysy);
    if (dist > maxDist) { // restore to starting position 
        // random starting position on fire-work's rim
        float angle = (sfIdx * 2 * pi)/NUM_SPARKS;
        float sinA = sin(angle);
        float cosA = cos(angle);
        float x = cosA * radius[fIdx];
        float y = sinA * radius[fIdx];

        position[index3j] = position[index3i] + x;
        position[index3j+1] = position[index3i+1] + y;
        position[index3j+2] = 0.0f;

        // travel scaled unit length 
        velocity[index3j] = cosA/5.0;
        velocity[index3j+1] = sinA/5.0;
        velocity[index3j+2] = 0.0f;
    }
}

// kernelAdvanceHypnosis   
//
// Update the radius/color of the circles
__global__ void kernelAdvanceHypnosis() { 
    int index = blockIdx.x * blockDim.x + threadIdx.x;
    if (index >= cuConstRendererParams.numCircles) 
        return; 

    float* radius = cuConstRendererParams.radius; 

    float cutOff = 0.5f;
    // place circle back in center after reaching threshold radisus 
    if (radius[index] > cutOff) { 
        radius[index] = 0.02f; 
    } else { 
        radius[index] += 0.01f; 
    }   
}   


// kernelAdvanceBouncingBalls
// 
// Update the positino of the balls
__global__ void kernelAdvanceBouncingBalls() { 
    const float dt = 1.f / 60.f;
    const float kGravity = -2.8f; // sorry Newton
    const float kDragCoeff = -0.8f;
    const float epsilon = 0.001f;

    int index = blockIdx.x * blockDim.x + threadIdx.x; 
   
    if (index >= cuConstRendererParams.numCircles) 
        return; 

    float* velocity = cuConstRendererParams.velocity; 
    float* position = cuConstRendererParams.position; 

    int index3 = 3 * index;
    // reverse velocity if center position < 0
    float oldVelocity = velocity[index3+1];
    float oldPosition = position[index3+1];

    if (oldVelocity == 0.f && oldPosition == 0.f) { // stop-condition 
        return;
    }

    if (position[index3+1] < 0 && oldVelocity < 0.f) { // bounce ball 
        velocity[index3+1] *= kDragCoeff;
    }

    // update velocity: v = u + at (only along y-axis)
    velocity[index3+1] += kGravity * dt;

    // update positions (only along y-axis)
    position[index3+1] += velocity[index3+1] * dt;

    if (fabsf(velocity[index3+1] - oldVelocity) < epsilon
        && oldPosition < 0.0f
        && fabsf(position[index3+1]-oldPosition) < epsilon) { // stop ball 
        velocity[index3+1] = 0.f;
        position[index3+1] = 0.f;
    }
}

// kernelAdvanceSnowflake -- (CUDA device code)
//
// move the snowflake animation forward one time step.  Updates circle
// positions and velocities.  Note how the position of the snowflake
// is reset if it moves off the left, right, or bottom of the screen.
__global__ void kernelAdvanceSnowflake() {

    int index = blockIdx.x * blockDim.x + threadIdx.x;

    if (index >= cuConstRendererParams.numCircles)
        return;

    const float dt = 1.f / 60.f;
    const float kGravity = -1.8f; // sorry Newton
    const float kDragCoeff = 2.f;

    int index3 = 3 * index;

    float* positionPtr = &cuConstRendererParams.position[index3];
    float* velocityPtr = &cuConstRendererParams.velocity[index3];

    // loads from global memory
    float3 position = *((float3*)positionPtr);
    float3 velocity = *((float3*)velocityPtr);

    // hack to make farther circles move more slowly, giving the
    // illusion of parallax
    float forceScaling = fmin(fmax(1.f - position.z, .1f), 1.f); // clamp

    // add some noise to the motion to make the snow flutter
    float3 noiseInput;
    noiseInput.x = 10.f * position.x;
    noiseInput.y = 10.f * position.y;
    noiseInput.z = 255.f * position.z;
    float2 noiseForce = cudaVec2CellNoise(noiseInput, index);
    noiseForce.x *= 7.5f;
    noiseForce.y *= 5.f;

    // drag
    float2 dragForce;
    dragForce.x = -1.f * kDragCoeff * velocity.x;
    dragForce.y = -1.f * kDragCoeff * velocity.y;

    // update positions
    position.x += velocity.x * dt;
    position.y += velocity.y * dt;

    // update velocities
    velocity.x += forceScaling * (noiseForce.x + dragForce.y) * dt;
    velocity.y += forceScaling * (kGravity + noiseForce.y + dragForce.y) * dt;

    float radius = cuConstRendererParams.radius[index];

    // if the snowflake has moved off the left, right or bottom of
    // the screen, place it back at the top and give it a
    // pseudorandom x position and velocity.
    if ( (position.y + radius < 0.f) ||
         (position.x + radius) < -0.f ||
         (position.x - radius) > 1.f)
    {
        noiseInput.x = 255.f * position.x;
        noiseInput.y = 255.f * position.y;
        noiseInput.z = 255.f * position.z;
        noiseForce = cudaVec2CellNoise(noiseInput, index);

        position.x = .5f + .5f * noiseForce.x;
        position.y = 1.35f + radius;

        // restart from 0 vertical velocity.  Choose a
        // pseudo-random horizontal velocity.
        velocity.x = 2.f * noiseForce.y;
        velocity.y = 0.f;
    }

    // store updated positions and velocities to global memory
    *((float3*)positionPtr) = position;
    *((float3*)velocityPtr) = velocity;
}

// shadePixel -- (CUDA device code)
//
// given a pixel and a circle, determines the contribution to the
// pixel from the circle.  Update of the image is done in this
// function.  Called by kernelRenderCircles()
__device__ __inline__ void
shadePixel(int circleIndex, float2 pixelCenter, float3 p, float4* imagePtr) {

    float diffX = p.x - pixelCenter.x;
    float diffY = p.y - pixelCenter.y;
    float pixelDist = diffX * diffX + diffY * diffY;

    float rad = cuConstRendererParams.radius[circleIndex];;
    float maxDist = rad * rad;

    // circle does not contribute to the image
    if (pixelDist > maxDist)
        return;

    float3 rgb;
    float alpha;

    // there is a non-zero contribution.  Now compute the shading value

    // suggestion: This conditional is in the inner loop.  Although it
    // will evaluate the same for all threads, there is overhead in
    // setting up the lane masks etc to implement the conditional.  It
    // would be wise to perform this logic outside of the loop next in
    // kernelRenderCircles.  (If feeling good about yourself, you
    // could use some specialized template magic).
    if (cuConstRendererParams.sceneName == SNOWFLAKES || cuConstRendererParams.sceneName == SNOWFLAKES_SINGLE_FRAME) {

        const float kCircleMaxAlpha = .5f;
        const float falloffScale = 4.f;

        float normPixelDist = sqrt(pixelDist) / rad;
        rgb = lookupColor(normPixelDist);

        float maxAlpha = .6f + .4f * (1.f-p.z);
        maxAlpha = kCircleMaxAlpha * fmaxf(fminf(maxAlpha, 1.f), 0.f); // kCircleMaxAlpha * clamped value
        alpha = maxAlpha * exp(-1.f * falloffScale * normPixelDist * normPixelDist);

    } else {
        // simple: each circle has an assigned color
        int index3 = 3 * circleIndex;
        rgb = *(float3*)&(cuConstRendererParams.color[index3]);
        alpha = .5f;
    }

    float oneMinusAlpha = 1.f - alpha;

    // BEGIN SHOULD-BE-ATOMIC REGION
    // global memory read

    float4 existingColor = *imagePtr;
    float4 newColor;
    newColor.x = alpha * rgb.x + oneMinusAlpha * existingColor.x;
    newColor.y = alpha * rgb.y + oneMinusAlpha * existingColor.y;
    newColor.z = alpha * rgb.z + oneMinusAlpha * existingColor.z;
    newColor.w = alpha + existingColor.w;

    // global memory write
    *imagePtr = newColor;

    // END SHOULD-BE-ATOMIC REGION
}

__device__ __inline__ void
shadePixelNoGlobal(int circleIndex,
                   float2 pixelCenter,
                   float3 p,
                   float4* imagePtr,
                   float* sRadius,
                   float3* sColor,
                   int offset) {

    float diffX = p.x - pixelCenter.x;
    float diffY = p.y - pixelCenter.y;
    float pixelDist = diffX * diffX + diffY * diffY;

    // float rad = cuConstRendererParams.radius[circleIndex];;
    float rad = sRadius[circleIndex - offset];
    float maxDist = rad * rad;

    // circle does not contribute to the image
    if (pixelDist > maxDist)
        return;

    float3 rgb;
    float alpha;

    // there is a non-zero contribution.  Now compute the shading value

    // suggestion: This conditional is in the inner loop.  Although it
    // will evaluate the same for all threads, there is overhead in
    // setting up the lane masks etc to implement the conditional.  It
    // would be wise to perform this logic outside of the loop next in
    // kernelRenderCircles.  (If feeling good about yourself, you
    // could use some specialized template magic).
    if (cuConstRendererParams.sceneName == SNOWFLAKES || cuConstRendererParams.sceneName == SNOWFLAKES_SINGLE_FRAME) {

        const float kCircleMaxAlpha = .5f;
        const float falloffScale = 4.f;

        float normPixelDist = sqrt(pixelDist) / rad;
        rgb = lookupColor(normPixelDist);

        float maxAlpha = .6f + .4f * (1.f-p.z);
        maxAlpha = kCircleMaxAlpha * fmaxf(fminf(maxAlpha, 1.f), 0.f); // kCircleMaxAlpha * clamped value
        alpha = maxAlpha * exp(-1.f * falloffScale * normPixelDist * normPixelDist);

    } else {
        // simple: each circle has an assigned color
        int index3 = 3 * circleIndex;
        // rgb = *(float3*)&(cuConstRendererParams.color[index3]);
        rgb = sColor[circleIndex - offset];
        alpha = .5f;
    }

    float oneMinusAlpha = 1.f - alpha;

    // BEGIN SHOULD-BE-ATOMIC REGION
    // global memory read

    float4 existingColor = *imagePtr;
    float4 newColor;
    newColor.x = alpha * rgb.x + oneMinusAlpha * existingColor.x;
    newColor.y = alpha * rgb.y + oneMinusAlpha * existingColor.y;
    newColor.z = alpha * rgb.z + oneMinusAlpha * existingColor.z;
    newColor.w = alpha + existingColor.w;

    // global memory write
    *imagePtr = newColor;

    // END SHOULD-BE-ATOMIC REGION
}

// kernelRenderCircles -- (CUDA device code)
//
// Each thread renders a circle.  Since there is no protection to
// ensure order of update or mutual exclusion on the output image, the
// resulting image will be incorrect.
__global__ void kernelRenderCircles() {

    int index = blockIdx.x * blockDim.x + threadIdx.x;

    if (index >= cuConstRendererParams.numCircles)
        return;
    
    // printf("num of circles: %d\n", cuConstRendererParams.numCircles);

    int index3 = 3 * index;

    // read position and radius
    float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
    float  rad = cuConstRendererParams.radius[index];

    // compute the bounding box of the circle. The bound is in integer
    // screen coordinates, so it's clamped to the edges of the screen.
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short minX = static_cast<short>(imageWidth * (p.x - rad));
    short maxX = static_cast<short>(imageWidth * (p.x + rad)) + 1;
    short minY = static_cast<short>(imageHeight * (p.y - rad));
    short maxY = static_cast<short>(imageHeight * (p.y + rad)) + 1;

    // a bunch of clamps.  Is there a CUDA built-in for this?
    short screenMinX = (minX > 0) ? ((minX < imageWidth) ? minX : imageWidth) : 0;
    short screenMaxX = (maxX > 0) ? ((maxX < imageWidth) ? maxX : imageWidth) : 0;
    short screenMinY = (minY > 0) ? ((minY < imageHeight) ? minY : imageHeight) : 0;
    short screenMaxY = (maxY > 0) ? ((maxY < imageHeight) ? maxY : imageHeight) : 0;

    // print out the bounding box
    // printf("minX: %d, maxX: %d, minY: %d, maxY: %d\n", minX, maxX, minY, maxY);

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    // for all pixels in the bonding box
    for (int pixelY=screenMinY; pixelY<screenMaxY; pixelY++) {
        float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + screenMinX)]);
        for (int pixelX=screenMinX; pixelX<screenMaxX; pixelX++) {
            float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                                 invHeight * (static_cast<float>(pixelY) + 0.5f));
            shadePixel(index, pixelCenterNorm, p, imgPtr);
            imgPtr++;
        }
    }
}

__global__ void kernelRenderCircles_par_pixel(int index) {
    int index3 = 3 * index;

    // read position and radius
    float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
    float  rad = cuConstRendererParams.radius[index];

    // compute the bounding box of the circle. The bound is in integer
    // screen coordinates, so it's clamped to the edges of the screen.
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short minX = static_cast<short>(imageWidth * (p.x - rad));
    short maxX = static_cast<short>(imageWidth * (p.x + rad)) + 1;
    short minY = static_cast<short>(imageHeight * (p.y - rad));
    short maxY = static_cast<short>(imageHeight * (p.y + rad)) + 1;

    // a bunch of clamps.  Is there a CUDA built-in for this?
    short screenMinX = (minX > 0) ? ((minX < imageWidth) ? minX : imageWidth) : 0;
    short screenMaxX = (maxX > 0) ? ((maxX < imageWidth) ? maxX : imageWidth) : 0;
    short screenMinY = (minY > 0) ? ((minY < imageHeight) ? minY : imageHeight) : 0;
    short screenMaxY = (maxY > 0) ? ((maxY < imageHeight) ? maxY : imageHeight) : 0;

    // // print out the bounding box
    // if (blockIdx.x == 0 && blockIdx.y == 0 && threadIdx.x == 0 && threadIdx.y == 0) {
    //     printf("minX: %d, maxX: %d, minY: %d, maxY: %d\n", minX, maxX, minY, maxY);
    // }

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
    int pixelY = blockIdx.y * blockDim.y + threadIdx.y;

    if (pixelX >= screenMinX && pixelX < screenMaxX && pixelY >= screenMinY && pixelY < screenMaxY) {
        float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + pixelX)]);
        float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                             invHeight * (static_cast<float>(pixelY) + 0.5f));
        shadePixel(index, pixelCenterNorm, p, imgPtr);
    }
}
////////////////////////////////////////////////////////////////////////////////////////


CudaRenderer::CudaRenderer() {
    image = NULL;

    numCircles = 0;
    position = NULL;
    velocity = NULL;
    color = NULL;
    radius = NULL;

    cudaDevicePosition = NULL;
    cudaDeviceVelocity = NULL;
    cudaDeviceColor = NULL;
    cudaDeviceRadius = NULL;
    cudaDeviceImageData = NULL;
}

CudaRenderer::~CudaRenderer() {

    if (image) {
        delete image;
    }

    if (position) {
        delete [] position;
        delete [] velocity;
        delete [] color;
        delete [] radius;
    }

    if (cudaDevicePosition) {
        cudaFree(cudaDevicePosition);
        cudaFree(cudaDeviceVelocity);
        cudaFree(cudaDeviceColor);
        cudaFree(cudaDeviceRadius);
        cudaFree(cudaDeviceImageData);
    }
}

const Image*
CudaRenderer::getImage() {

    // need to copy contents of the rendered image from device memory
    // before we expose the Image object to the caller

    printf("Copying image data from device\n");

    cudaMemcpy(image->data,
               cudaDeviceImageData,
               sizeof(float) * 4 * image->width * image->height,
               cudaMemcpyDeviceToHost);

    return image;
}

void
CudaRenderer::loadScene(SceneName scene) {
    sceneName = scene;
    loadCircleScene(sceneName, numCircles, position, velocity, color, radius);
}

void
CudaRenderer::setup() {

    int deviceCount = 0;
    std::string name;
    cudaError_t err = cudaGetDeviceCount(&deviceCount);

    printf("---------------------------------------------------------\n");
    printf("Initializing CUDA for CudaRenderer\n");
    printf("Found %d CUDA devices\n", deviceCount);

    for (int i=0; i<deviceCount; i++) {
        cudaDeviceProp deviceProps;
        cudaGetDeviceProperties(&deviceProps, i);
        name = deviceProps.name;

        printf("Device %d: %s\n", i, deviceProps.name);
        printf("   SMs:        %d\n", deviceProps.multiProcessorCount);
        printf("   Global mem: %.0f MB\n", static_cast<float>(deviceProps.totalGlobalMem) / (1024 * 1024));
        printf("   CUDA Cap:   %d.%d\n", deviceProps.major, deviceProps.minor);
    }
    printf("---------------------------------------------------------\n");
    
    // By this time the scene should be loaded.  Now copy all the key
    // data structures into device memory so they are accessible to
    // CUDA kernels
    //
    // See the CUDA Programmer's Guide for descriptions of
    // cudaMalloc and cudaMemcpy

    cudaMalloc(&cudaDevicePosition, sizeof(float) * 3 * numCircles);
    cudaMalloc(&cudaDeviceVelocity, sizeof(float) * 3 * numCircles);
    cudaMalloc(&cudaDeviceColor, sizeof(float) * 3 * numCircles);
    cudaMalloc(&cudaDeviceRadius, sizeof(float) * numCircles);
    cudaMalloc(&cudaDeviceImageData, sizeof(float) * 4 * image->width * image->height);

    cudaMemcpy(cudaDevicePosition, position, sizeof(float) * 3 * numCircles, cudaMemcpyHostToDevice);
    cudaMemcpy(cudaDeviceVelocity, velocity, sizeof(float) * 3 * numCircles, cudaMemcpyHostToDevice);
    cudaMemcpy(cudaDeviceColor, color, sizeof(float) * 3 * numCircles, cudaMemcpyHostToDevice);
    cudaMemcpy(cudaDeviceRadius, radius, sizeof(float) * numCircles, cudaMemcpyHostToDevice);

    // Initialize parameters in constant memory.  We didn't talk about
    // constant memory in class, but the use of read-only constant
    // memory here is an optimization over just sticking these values
    // in device global memory.  NVIDIA GPUs have a few special tricks
    // for optimizing access to constant memory.  Using global memory
    // here would have worked just as well.  See the Programmer's
    // Guide for more information about constant memory.

    GlobalConstants params;
    params.sceneName = sceneName;
    params.numCircles = numCircles;
    params.imageWidth = image->width;
    params.imageHeight = image->height;
    params.position = cudaDevicePosition;
    params.velocity = cudaDeviceVelocity;
    params.color = cudaDeviceColor;
    params.radius = cudaDeviceRadius;
    params.imageData = cudaDeviceImageData;

    cudaMemcpyToSymbol(cuConstRendererParams, &params, sizeof(GlobalConstants));

    // also need to copy over the noise lookup tables, so we can
    // implement noise on the GPU
    int* permX;
    int* permY;
    float* value1D;
    getNoiseTables(&permX, &permY, &value1D);
    cudaMemcpyToSymbol(cuConstNoiseXPermutationTable, permX, sizeof(int) * 256);
    cudaMemcpyToSymbol(cuConstNoiseYPermutationTable, permY, sizeof(int) * 256);
    cudaMemcpyToSymbol(cuConstNoise1DValueTable, value1D, sizeof(float) * 256);

    // last, copy over the color table that's used by the shading
    // function for circles in the snowflake demo

    float lookupTable[COLOR_MAP_SIZE][3] = {
        {1.f, 1.f, 1.f},
        {1.f, 1.f, 1.f},
        {.8f, .9f, 1.f},
        {.8f, .9f, 1.f},
        {.8f, 0.8f, 1.f},
    };

    cudaMemcpyToSymbol(cuConstColorRamp, lookupTable, sizeof(float) * 3 * COLOR_MAP_SIZE);

}

// allocOutputImage --
//
// Allocate buffer the renderer will render into.  Check status of
// image first to avoid memory leak.
void
CudaRenderer::allocOutputImage(int width, int height) {

    if (image)
        delete image;
    image = new Image(width, height);
}

// clearImage --
//
// Clear's the renderer's target image.  The state of the image after
// the clear depends on the scene being rendered.
void
CudaRenderer::clearImage() {

    // 256 threads per block is a healthy number
    dim3 blockDim(16, 16, 1);
    dim3 gridDim(
        (image->width + blockDim.x - 1) / blockDim.x,
        (image->height + blockDim.y - 1) / blockDim.y);

    if (sceneName == SNOWFLAKES || sceneName == SNOWFLAKES_SINGLE_FRAME) {
        kernelClearImageSnowflake<<<gridDim, blockDim>>>();
    } else {
        kernelClearImage<<<gridDim, blockDim>>>(1.f, 1.f, 1.f, 1.f);
    }
    cudaDeviceSynchronize();
}

// advanceAnimation --
//
// Advance the simulation one time step.  Updates all circle positions
// and velocities
void
CudaRenderer::advanceAnimation() {
     // 256 threads per block is a healthy number
    dim3 blockDim(256, 1);
    dim3 gridDim((numCircles + blockDim.x - 1) / blockDim.x);

    // only the snowflake scene has animation
    if (sceneName == SNOWFLAKES) {
        kernelAdvanceSnowflake<<<gridDim, blockDim>>>();
    } else if (sceneName == BOUNCING_BALLS) {
        kernelAdvanceBouncingBalls<<<gridDim, blockDim>>>();
    } else if (sceneName == HYPNOSIS) {
        kernelAdvanceHypnosis<<<gridDim, blockDim>>>();
    } else if (sceneName == FIREWORKS) { 
        kernelAdvanceFireWorks<<<gridDim, blockDim>>>(); 
    }
    cudaDeviceSynchronize();
}

// void
// CudaRenderer::render() {

//     // 256 threads per block is a healthy number
//     dim3 blockDim(256, 1);
//     dim3 gridDim((numCircles + blockDim.x - 1) / blockDim.x);

//     kernelRenderCircles<<<gridDim, blockDim>>>();
//     cudaDeviceSynchronize();
// }

// // pixel parallel version
// void
// CudaRenderer::render() {
//     dim3 blockDim(16, 16, 1);
//     dim3 gridDim(
//         (image->width + blockDim.x - 1) / blockDim.x,
//         (image->height + blockDim.y - 1) / blockDim.y);
//     // printf("Grid dim: %d, %d\n", gridDim.x, gridDim.y);
//     // printf("Block dim: %d, %d, %d\n", blockDim.x, blockDim.y, blockDim.z);
//     // printf("Number of circles: %d\n", numCircles);
//     // printf("Image width: %d, height: %d\n", image->width, image->height);
//     for (int i = 0; i < numCircles; i++) {
//         // printf("Rendering circle %d\n", i);
//         kernelRenderCircles_par_pixel<<<gridDim, blockDim>>>(i);
//         // cudaCheckError(cudaDeviceSynchronize());
//         cudaCheckError(cudaGetLastError());
//     }
//     cudaCheckError(cudaDeviceSynchronize());
// 

__device__ void blockKernelRenderCircles(int numIndependentCircles) {

    // int index = blockIdx.x * blockDim.x + threadIdx.x;

    // if (index >= cuConstRendererParams.numCircles)
    //     return;

    // printf("num of circles: %d\n", cuConstRendererParams.numCircles);

    // block id is index
    int index = blockIdx.x;
    int thread_id = threadIdx.x;
    int index3 = 3 * index;

    // read position and radius
    float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
    float  rad = cuConstRendererParams.radius[index];

    // compute the bounding box of the circle. The bound is in integer
    // screen coordinates, so it's clamped to the edges of the screen.
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short minX = static_cast<short>(imageWidth * (p.x - rad));
    short maxX = static_cast<short>(imageWidth * (p.x + rad)) + 1;
    short minY = static_cast<short>(imageHeight * (p.y - rad));
    short maxY = static_cast<short>(imageHeight * (p.y + rad)) + 1;

    // a bunch of clamps.  Is there a CUDA built-in for this?
    short screenMinX = (minX > 0) ? ((minX < imageWidth) ? minX : imageWidth) : 0;
    short screenMaxX = (maxX > 0) ? ((maxX < imageWidth) ? maxX : imageWidth) : 0;
    short screenMinY = (minY > 0) ? ((minY < imageHeight) ? minY : imageHeight) : 0;
    short screenMaxY = (maxY > 0) ? ((maxY < imageHeight) ? maxY : imageHeight) : 0;

    // print out the bounding box
    // printf("minX: %d, maxX: %d, minY: %d, maxY: %d\n", minX, maxX, minY, maxY);

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    int pixel_num = (screenMaxX - screenMinX) * (screenMaxY - screenMinY);
    for (int pixel_id = thread_id; pixel_id < pixel_num; pixel_id += blockDim.x) {
        int pixelX = pixel_id % (screenMaxX - screenMinX) + screenMinX;
        int pixelY = pixel_id / (screenMaxX - screenMinX) + screenMinY;
        // for all pixels in the bonding box
        float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + pixelX)]);
        float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                             invHeight * (static_cast<float>(pixelY) + 0.5f));
        shadePixel(index, pixelCenterNorm, p, imgPtr);
    }
}



// Shared memory version of shadePixel that works on shared memory tile
__device__ __inline__ void
shadePixelTiled(int circleIndex, float2 pixelCenter, float3 p, float4* sharedTile,
                int tileX, int tileY, int tileWidth) {

    float diffX = p.x - pixelCenter.x;
    float diffY = p.y - pixelCenter.y;
    float pixelDist = diffX * diffX + diffY * diffY;

    float rad = cuConstRendererParams.radius[circleIndex];
    float maxDist = rad * rad;

    // circle does not contribute to the image
    if (pixelDist > maxDist)
        return;

    float3 rgb;
    float alpha;

    // there is a non-zero contribution.  Now compute the shading value
    if (cuConstRendererParams.sceneName == SNOWFLAKES || cuConstRendererParams.sceneName == SNOWFLAKES_SINGLE_FRAME) {
        const float kCircleMaxAlpha = .5f;
        const float falloffScale = 4.f;

        float normPixelDist = sqrt(pixelDist) / rad;
        rgb = lookupColor(normPixelDist);

        float maxAlpha = .6f + .4f * (1.f-p.z);
        maxAlpha = kCircleMaxAlpha * fmaxf(fminf(maxAlpha, 1.f), 0.f);
        alpha = maxAlpha * exp(-1.f * falloffScale * normPixelDist * normPixelDist);
    } else {
        // simple: each circle has an assigned color
        int index3 = 3 * circleIndex;
        rgb = *(float3*)&(cuConstRendererParams.color[index3]);
        alpha = .5f;
    }

    float oneMinusAlpha = 1.f - alpha;

    // Work on shared memory tile instead of global memory
    int tileIndex = tileY * tileWidth + tileX;
    float4 existingColor = sharedTile[tileIndex];
    float4 newColor;
    newColor.x = alpha * rgb.x + oneMinusAlpha * existingColor.x;
    newColor.y = alpha * rgb.y + oneMinusAlpha * existingColor.y;
    newColor.z = alpha * rgb.z + oneMinusAlpha * existingColor.z;
    newColor.w = alpha + existingColor.w;

    sharedTile[tileIndex] = newColor;
}

__device__ void deviceRenderIndependentCircles(int* independent_indices, int num_independent) {
    if (blockIdx.x >= num_independent)
        return;

    // Get the actual circle index from the independent_indices array
    int circle_index = independent_indices[blockIdx.x];
    int thread_id = threadIdx.x;
    int index3 = 3 * circle_index;

    // Shared memory for bounding box info
    __shared__ float3 shared_position;
    __shared__ float shared_radius;
    __shared__ short shared_screenMinX, shared_screenMaxX, shared_screenMinY, shared_screenMaxY;
    __shared__ float shared_invWidth, shared_invHeight;
    __shared__ int tileWidth, tileHeight;

    // Thread 0 loads circle data and computes bounding box
    if (thread_id == 0) {
        // Load circle data
        shared_position = *(float3*)(&cuConstRendererParams.position[index3]);
        shared_radius = cuConstRendererParams.radius[circle_index];

        // compute the bounding box of the circle
        short imageWidth = cuConstRendererParams.imageWidth;
        short imageHeight = cuConstRendererParams.imageHeight;
        short minX = static_cast<short>(imageWidth * (shared_position.x - shared_radius));
        short maxX = static_cast<short>(imageWidth * (shared_position.x + shared_radius)) + 1;
        short minY = static_cast<short>(imageHeight * (shared_position.y - shared_radius));
        short maxY = static_cast<short>(imageHeight * (shared_position.y + shared_radius)) + 1;

        // clamp to screen bounds
        shared_screenMinX = (minX > 0) ? ((minX < imageWidth) ? minX : imageWidth) : 0;
        shared_screenMaxX = (maxX > 0) ? ((maxX < imageWidth) ? maxX : imageWidth) : 0;
        shared_screenMinY = (minY > 0) ? ((minY < imageHeight) ? minY : imageHeight) : 0;
        shared_screenMaxY = (maxY > 0) ? ((maxY < imageHeight) ? maxY : imageHeight) : 0;

        shared_invWidth = 1.f / imageWidth;
        shared_invHeight = 1.f / imageHeight;

        tileWidth = shared_screenMaxX - shared_screenMinX;
        tileHeight = shared_screenMaxY - shared_screenMinY;
    }

    // Synchronize to ensure all threads see the loaded data
    __syncthreads();

    // Calculate tile size and allocate shared memory for image tile
    int tileSize = tileWidth * tileHeight;
    if (tileSize <= 0) return; // No pixels to process

    // Process the bounding box in 32x32 tiles
    const int TILE_SIZE = 32;

    // Calculate how many tiles we need to cover the bounding box
    int tilesX = (tileWidth + TILE_SIZE - 1) / TILE_SIZE;  // Ceiling division
    int tilesY = (tileHeight + TILE_SIZE - 1) / TILE_SIZE;

    // Process each tile
    for (int tileY_idx = 0; tileY_idx < tilesY; tileY_idx++) {
        for (int tileX_idx = 0; tileX_idx < tilesX; tileX_idx++) {

            // Calculate current tile bounds
            int currentTileStartX = tileX_idx * TILE_SIZE;
            int currentTileStartY = tileY_idx * TILE_SIZE;
            int currentTileEndX = (currentTileStartX + TILE_SIZE < tileWidth) ? currentTileStartX + TILE_SIZE : tileWidth;
            int currentTileEndY = (currentTileStartY + TILE_SIZE < tileHeight) ? currentTileStartY + TILE_SIZE : tileHeight;
            int currentTileWidth = currentTileEndX - currentTileStartX;
            int currentTileHeight = currentTileEndY - currentTileStartY;
            int currentTilePixels = currentTileWidth * currentTileHeight;

            // Dynamically allocate shared memory for the image tile
            extern __shared__ float4 sharedImageTile[];

            // Load current tile into shared memory
            for (int i = thread_id; i < currentTilePixels; i += blockDim.x) {
                int localTileX = i % currentTileWidth;
                int localTileY = i / currentTileWidth;
                int globalX = shared_screenMinX + currentTileStartX + localTileX;
                int globalY = shared_screenMinY + currentTileStartY + localTileY;

                int globalIndex = globalY * cuConstRendererParams.imageWidth + globalX;
                sharedImageTile[i] = *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]);
            }

            // Synchronize after loading tile
            __syncthreads();

            // Process pixels in current tile using shared memory
            for (int i = thread_id; i < currentTilePixels; i += blockDim.x) {
                int localTileX = i % currentTileWidth;
                int localTileY = i / currentTileWidth;
                int globalX = shared_screenMinX + currentTileStartX + localTileX;
                int globalY = shared_screenMinY + currentTileStartY + localTileY;

                float2 pixelCenterNorm = make_float2(shared_invWidth * (static_cast<float>(globalX) + 0.5f),
                                                     shared_invHeight * (static_cast<float>(globalY) + 0.5f));

                shadePixelTiled(circle_index, pixelCenterNorm, shared_position,
                               sharedImageTile, localTileX, localTileY, currentTileWidth);
            }

            // Synchronize after processing
            __syncthreads();

            // Write current tile back to global memory
            for (int i = thread_id; i < currentTilePixels; i += blockDim.x) {
                int localTileX = i % currentTileWidth;
                int localTileY = i / currentTileWidth;
                int globalX = shared_screenMinX + currentTileStartX + localTileX;
                int globalY = shared_screenMinY + currentTileStartY + localTileY;

                int globalIndex = globalY * cuConstRendererParams.imageWidth + globalX;
                *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]) = sharedImageTile[i];
            }

            // Synchronize before moving to next tile
            __syncthreads();
        }
    }
}

// Global kernel with shared memory tiling for image data
__global__
void kernelRenderIndependentCircles(int* independent_indices, int num_independent) {
    // for (int i = blockIdx.x; i < num_independent; i += gridDim.x) {
    //     deviceRenderIndependentCircles(independent_indices, num_independent);
    // }
    deviceRenderIndependentCircles(independent_indices, num_independent);
}

void pixelParallelRender(int numCircles, Image* image, const thrust::host_vector<int>& comparison_mask) {
    dim3 blockDim(16, 16, 1);
    dim3 gridDim(
        (image->width + blockDim.x - 1) / blockDim.x,
        (image->height + blockDim.y - 1) / blockDim.y);
    // printf("Grid dim: %d, %d\n", gridDim.x, gridDim.y);
    // printf("Block dim: %d, %d, %d\n", blockDim.x, blockDim.y, blockDim.z);
    // printf("Number of circles: %d\n", numCircles);
    // printf("Image width: %d, height: %d\n", image->width, image->height);
    for (int i = 0; i < numCircles; i++) {
        if (comparison_mask[i] == 0) continue;
        // printf("Rendering circle %d\n", i);
        kernelRenderCircles_par_pixel<<<gridDim, blockDim>>>(i);
        // cudaCheckError(cudaDeviceSynchronize());
        cudaCheckError(cudaGetLastError());
    }
    cudaCheckError(cudaDeviceSynchronize());
}

__device__ void indexKernelRenderCircles(int index, int numCircles) {

    // int index = blockIdx.x * blockDim.x + threadIdx.x;

    // if (index >= cuConstRendererParams.numCircles)
    //     return;
    
    // printf("num of circles: %d\n", cuConstRendererParams.numCircles);

    int index3 = 3 * index;

    // read position and radius
    float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
    float  rad = cuConstRendererParams.radius[index];

    // compute the bounding box of the circle. The bound is in integer
    // screen coordinates, so it's clamped to the edges of the screen.
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short minX = static_cast<short>(imageWidth * (p.x - rad));
    short maxX = static_cast<short>(imageWidth * (p.x + rad)) + 1;
    short minY = static_cast<short>(imageHeight * (p.y - rad));
    short maxY = static_cast<short>(imageHeight * (p.y + rad)) + 1;

    // a bunch of clamps.  Is there a CUDA built-in for this?
    short screenMinX = (minX > 0) ? ((minX < imageWidth) ? minX : imageWidth) : 0;
    short screenMaxX = (maxX > 0) ? ((maxX < imageWidth) ? maxX : imageWidth) : 0;
    short screenMinY = (minY > 0) ? ((minY < imageHeight) ? minY : imageHeight) : 0;
    short screenMaxY = (maxY > 0) ? ((maxY < imageHeight) ? maxY : imageHeight) : 0;

    // print out the bounding box
    // printf("minX: %d, maxX: %d, minY: %d, maxY: %d\n", minX, maxX, minY, maxY);

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    // for all pixels in the bonding box
    for (int pixelY=screenMinY; pixelY<screenMaxY; pixelY++) {
        float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + screenMinX)]);
        for (int pixelX=screenMinX; pixelX<screenMaxX; pixelX++) {
            float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                                 invHeight * (static_cast<float>(pixelY) + 0.5f));
            shadePixel(index, pixelCenterNorm, p, imgPtr);
            imgPtr++;
        }
    }
}

# include "circleBoxTest.cu_inl"
int render_independent_circles(int numCircles,
                               thrust::device_vector<int>& comparison_mask) {
    nvtxRangePush("render_independent_circles");

    // find all circles without overlapping
    // 1.f means no overlap with former, 0.f means overlap with former
    nvtxRangePush("initialize_independent_flags");
    thrust::device_vector<int> independent_flags = comparison_mask;
    nvtxRangePop();

    nvtxRangePush("check_circle_overlaps");
    int num_circles = numCircles;  // Create local copy for lambda capture
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(independent_flags.data()),
                      mask_ptr = thrust::raw_pointer_cast(comparison_mask.data()),
                      num_circles] __device__ (int i) {
        if (mask_ptr[i] == 0) {
            return;
        }
        float *pos = &cuConstRendererParams.position[3*i];
        float *rad = &cuConstRendererParams.radius[i];
        float boxL = pos[0] - rad[0];
        float boxR = pos[0] + rad[0];
        float boxT = pos[1] + rad[0];
        float boxB = pos[1] - rad[0];
        for (int j = 0; j < i; j++) {
            // Skip comparison if the circle j is masked out
            if (mask_ptr[j] == 0) {
                continue;
            }

            float *posj = &cuConstRendererParams.position[3*j];
            float *radj = &cuConstRendererParams.radius[j];
            if (circleInBoxConservative(
                posj[0], posj[1], radj[0],
                boxL, boxR, boxT, boxB
            )) {
                independent_flags_ptr[i] = 0;
                break;
            }
        }
    });
    nvtxRangePop();

    // set mask
    nvtxRangePush("update_comparison_mask");
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(independent_flags.data()),
                      mask_ptr = thrust::raw_pointer_cast(comparison_mask.data())] __device__ (int i) {
        if (independent_flags_ptr[i] == 1) {
            mask_ptr[i] = 0;
        }
    });
    nvtxRangePop();

    nvtxRangePush("exclusive_scan_and_gather");
    thrust::device_vector<int> ex_vec = independent_flags;
    // do exclusive scan
    thrust::exclusive_scan(thrust::device,
                           ex_vec.begin(),
                           ex_vec.end(),
                           ex_vec.begin());
    
    // gather all the independent circles
    int num_independent = ex_vec[numCircles-1] + independent_flags[numCircles-1];
    thrust::device_vector<int> independent_indices(num_independent);
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(independent_flags.data()),
                      ex_vec_ptr = thrust::raw_pointer_cast(ex_vec.data()),
                      independent_indices_ptr = thrust::raw_pointer_cast(independent_indices.data())] __device__ (int i) {
        if (independent_flags_ptr[i] == 1) {
            independent_indices_ptr[ex_vec_ptr[i]] = i;
        }
    });
    nvtxRangePop();

    // render independent circles in parallel using blockKernelRenderCircles
    nvtxRangePush("render_independent_circles_parallel");

    // Launch kernel with one block per independent circle
    // Each block will have multiple threads to process pixels in parallel
    dim3 blockDim(256);  // 256 threads per block for pixel processing
    dim3 gridDim(num_independent);  // One block per independent circle

    // Calculate maximum possible tile size for shared memory allocation
    // RTX 3090 has 48KB shared memory per SM, float4 = 16 bytes
    // So max elements = 48KB / 16B = 3072 elements, use 32x32 = 1024 for safety
    int maxTileSize = 32 * 32; // 32x32 tile maximum (16KB)
    size_t sharedMemSize = maxTileSize * sizeof(float4);

    kernelRenderIndependentCircles<<<gridDim, blockDim, sharedMemSize>>>(
        thrust::raw_pointer_cast(independent_indices.data()),
        num_independent
    );
    cudaCheckError(cudaGetLastError());
    nvtxRangePop();

    nvtxRangePop(); // End of render_independent_circles
    return num_independent;
}
int render_independent_circles_in_block(int numCircles,
                               thrust::device_vector<int>& comparison_mask,
                               thrust::device_vector<int>& persistent_independent_flags,
                               thrust::device_vector<int>& persistent_ex_vec,
                               thrust::device_vector<int>& persistent_independent_indices) {
    nvtxRangePush("render_independent_circles");

    // find all circles without overlapping
    // 1.f means no overlap with former, 0.f means overlap with former
    nvtxRangePush("initialize_independent_flags");
    // Reuse persistent vector instead of creating new one
    persistent_independent_flags = comparison_mask;
    
    nvtxRangePop();

    nvtxRangePush("check_circle_overlaps");
    int num_circles = numCircles;  // Create local copy for lambda capture
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(persistent_independent_flags.data()),
                      mask_ptr = thrust::raw_pointer_cast(comparison_mask.data()),
                      num_circles] __device__ (int i) {
        if (mask_ptr[i] == 0) {
            return;
        }
        float *pos = &cuConstRendererParams.position[3*i];
        float *rad = &cuConstRendererParams.radius[i];
        float boxL = pos[0] - rad[0];
        float boxR = pos[0] + rad[0];
        float boxT = pos[1] + rad[0];
        float boxB = pos[1] - rad[0];
        for (int j = 0; j < i; j++) {
            // Skip comparison if the circle j is masked out
            if (mask_ptr[j] == 0) {
                continue;
            }

            float *posj = &cuConstRendererParams.position[3*j];
            float *radj = &cuConstRendererParams.radius[j];
            if (circleInBoxConservative(
                posj[0], posj[1], radj[0],
                boxL, boxR, boxT, boxB
            )) {
                independent_flags_ptr[i] = 0;
                break;
            }
        }
    });
    nvtxRangePop();

    // set mask
    nvtxRangePush("update_comparison_mask");
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(persistent_independent_flags.data()),
                      mask_ptr = thrust::raw_pointer_cast(comparison_mask.data())] __device__ (int i) {
        if (independent_flags_ptr[i] == 1) {
            mask_ptr[i] = 0;
        }
    });
    nvtxRangePop();

    nvtxRangePush("exclusive_scan_and_gather");
    // Reuse persistent vector instead of creating new one
    persistent_ex_vec = persistent_independent_flags;
    // do exclusive scan
    thrust::exclusive_scan(thrust::device,
                           persistent_ex_vec.begin(),
                           persistent_ex_vec.end(),
                           persistent_ex_vec.begin());

    // gather all the independent circles
    int num_independent = persistent_ex_vec[numCircles-1] + persistent_independent_flags[numCircles-1];
    // Resize persistent vector to fit the current number of independent circles
    persistent_independent_indices.resize(num_independent);
    thrust::for_each(thrust::device,
                     thrust::counting_iterator<int>(0),
                     thrust::counting_iterator<int>(numCircles),
                     [independent_flags_ptr = thrust::raw_pointer_cast(persistent_independent_flags.data()),
                      ex_vec_ptr = thrust::raw_pointer_cast(persistent_ex_vec.data()),
                      independent_indices_ptr = thrust::raw_pointer_cast(persistent_independent_indices.data())] __device__ (int i) {
        if (independent_flags_ptr[i] == 1) {
            independent_indices_ptr[ex_vec_ptr[i]] = i;
        }
    });
    nvtxRangePop();

    // render independent circles in parallel using blockKernelRenderCircles
    nvtxRangePush("render_independent_circles_parallel");

    // Launch kernel with one block per independent circle
    // Each block will have multiple threads to process pixels in parallel
    dim3 blockDim(256);  // 256 threads per block for pixel processing
    dim3 gridDim(num_independent);  // One block per independent circle

    // Calculate maximum possible tile size for shared memory allocation
    // RTX 3090 has 48KB shared memory per SM, float4 = 16 bytes
    // So max elements = 48KB / 16B = 3072 elements, use 32x32 = 1024 for safety
    int maxTileSize = 32 * 32; // 32x32 tile maximum (16KB)
    size_t sharedMemSize = maxTileSize * sizeof(float4);

    kernelRenderIndependentCircles<<<gridDim, blockDim, sharedMemSize>>>(
        thrust::raw_pointer_cast(persistent_independent_indices.data()),
        num_independent
    );
    // cudaCheckError(cudaGetLastError());
    nvtxRangePop();

    nvtxRangePop(); // End of render_independent_circles
    return num_independent;
}
#define DEBUG_BLOCK 0
#define SCAN_BLOCK_DIM 256
#include "exclusiveScan.cu_inl"
__device__ int blockGatherIndependentCirclesIteration(
    int num_indep_circles,
    float* sPosition,
    float* sRadius,
    uint* sIndependentFlags,
    uint* sMask,
    uint* sExclusiveScanResult,
    uint* sIndependentIndices,
    uint* sScratch,
    int scan_block_dim,
    int threadIdx_x
) {
    // set independent flags 
    if (threadIdx_x < scan_block_dim) {
        float boxL = sPosition[threadIdx_x*3] - sRadius[threadIdx_x];
        float boxR = sPosition[threadIdx_x*3] + sRadius[threadIdx_x];
        float boxT = sPosition[threadIdx_x*3+1] + sRadius[threadIdx_x];
        float boxB = sPosition[threadIdx_x*3+1] - sRadius[threadIdx_x];
        int cur_index = threadIdx_x;
        for (int j = 0; j < cur_index; j++) {
            if (sMask[j] == 0) {
                continue;
            }
            float* pos_j_ptr = (float*)&sPosition[j*3];
            float* rad_j_ptr = &sRadius[j];
            if (circleInBoxConservative(
                pos_j_ptr[0], pos_j_ptr[1], rad_j_ptr[0],
                boxL, boxR, boxT, boxB
            )) {
                sIndependentFlags[threadIdx_x] = 0;
                break;
            }
        }
    }
    __syncthreads();
    // 2. set mask
    if (sIndependentFlags[threadIdx_x] == 1) {
        sMask[threadIdx_x] = 0;
    }
    // // print sMask
    // if (threadIdx_x == 0 && blockIdx.x == DEBUG_BLOCK) {
    //     for (int i = 0; i < scan_block_dim; i++) {
    //         printf("sMask[%d] = %d, ", i, sMask[i]);
    //     }
    //     printf("\n");
    // }
    // 3. exclusive scan
    sharedMemExclusiveScan(threadIdx_x,
                           sIndependentFlags,
                           sExclusiveScanResult,
                           sScratch,
                           SCAN_BLOCK_DIM);
    __syncthreads();
    // // print sExclusiveScanResult
    // if (threadIdx_x == 0 && blockIdx.x == DEBUG_BLOCK) {
    //     for (int i = 0; i < scan_block_dim; i++) {
    //         printf("sExclusiveScanResult[%d] = %d, ", i, sExclusiveScanResult[i]);
    //     }
    //     printf("\n");
    // }
    int num_independent = sExclusiveScanResult[scan_block_dim-1] + sIndependentFlags[scan_block_dim-1]; 

    // 4. gather
    if (sIndependentFlags[threadIdx_x] == 1) {
        sIndependentIndices[num_indep_circles + sExclusiveScanResult[threadIdx_x]] = threadIdx_x;
        // if (blockIdx.x == DEBUG_BLOCK) {
        //     printf("sIndependentIndices[%d] = %d\n", num_indep_circles + sExclusiveScanResult[threadIdx_x], threadIdx_x);
        // }
    }
    __syncthreads();
    return num_independent;
}
/**
 * `independent_indices_ptr` contains all circles' indices in each block, 
 * `independent_indices_len_ptr` contains # of independent circles in each block
 * 
 * for example, [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15] 
 * the independent_indices_ptr is [|0, 4, 5|, |1|, |2, 6, 9, 10|, |3, 7, 11|, |8|, |12, 13, 14, 15|]
 * then the independent_indices_len_ptr is [3, 1, 4, 3, 1, 4]
 */
__global__
void blockGatherIndependentCircles(int num_circles,
                                   int* independent_indices_ptr,
                                   int* independent_indices_len_ptr) {
    // 1. check overlap in block
    int i = blockDim.x * blockIdx.x + threadIdx.x;
    __shared__ float sPosition[SCAN_BLOCK_DIM*3];
    __shared__ float sRadius[SCAN_BLOCK_DIM];
    __shared__ uint sIndependentFlags[SCAN_BLOCK_DIM];
    __shared__ uint sMask[SCAN_BLOCK_DIM];
    __shared__ uint sExclusiveScanResult[SCAN_BLOCK_DIM];
    __shared__ uint sIndependentIndices[SCAN_BLOCK_DIM];
    __shared__ uint sIndependentIndicesLen[SCAN_BLOCK_DIM];
    __shared__ uint sScratch[SCAN_BLOCK_DIM * 2];
    // load to shared memory
    if (i < num_circles) {
        float *pos = &cuConstRendererParams.position[3*i];
        float *rad = &cuConstRendererParams.radius[i];
        sPosition[threadIdx.x*3] = pos[0];
        sPosition[threadIdx.x*3+1] = pos[1];
        sPosition[threadIdx.x*3+2] = pos[2];
        sRadius[threadIdx.x] = rad[0];
        sIndependentFlags[threadIdx.x] = 1;
        sMask[threadIdx.x] = 1;
    } else {
        sPosition[threadIdx.x*3] = 0;
        sPosition[threadIdx.x*3+1] = 0;
        sPosition[threadIdx.x*3+2] = 0;
        sRadius[threadIdx.x] = 0;
        sIndependentFlags[threadIdx.x] = 0;
        sMask[threadIdx.x] = 0;
    }
    __syncthreads();
    int num_independent = 0;
    int scan_block_dim = SCAN_BLOCK_DIM;
    // int num_circles_per_block = scan_block_dim;
    // if it is the last block, set num_circles_per_block to the remaining circles
    if (blockIdx.x == gridDim.x - 1) {
        scan_block_dim = num_circles - blockIdx.x * SCAN_BLOCK_DIM;
    }
    int num_indep_groups = 0;
    int num_indep_circles = 0;
    do {
        num_independent = blockGatherIndependentCirclesIteration(
            num_indep_circles,
            sPosition,
            sRadius,
            sIndependentFlags,
            sMask,
            sExclusiveScanResult,
            sIndependentIndices,
            sScratch,
            scan_block_dim,
            threadIdx.x
        );
        sIndependentIndicesLen[num_indep_groups] = num_independent;
        sIndependentFlags[threadIdx.x] = sMask[threadIdx.x];
        num_indep_groups++;
        num_indep_circles += num_independent;
    } while (num_indep_circles < scan_block_dim);
    __syncthreads();
    // 5. gather - convert local thread indices to global circle indices
    if (threadIdx.x < scan_block_dim) {
        // Convert local thread index to global circle index
        int global_circle_index = blockIdx.x * SCAN_BLOCK_DIM + sIndependentIndices[threadIdx.x];
        independent_indices_ptr[blockIdx.x * SCAN_BLOCK_DIM + threadIdx.x] = global_circle_index;
        independent_indices_len_ptr[blockIdx.x * SCAN_BLOCK_DIM + threadIdx.x] = sIndependentIndicesLen[threadIdx.x];
    }
}
__global__
void renderIndependentCirclesKernel(int numCircles,
                                    int* independent_indices_ptr,
                                    int* independent_indices_len_ptr) {
    int total_num_processed = 0; // # independent circles have been rendered in all segments

    for (int i = 0; i < numCircles; i += SCAN_BLOCK_DIM) {
        int *independent_indices_iteration_ptr = independent_indices_ptr + i;
        int *independent_indices_iteration_len_ptr = independent_indices_len_ptr + i;
        int indices_len_index = 0; // index of len of current segment in independent_indices_len_ptr
        int segment_num_processed = 0; // # independent circles have been rendered in current segment
        while (true) {
            // get # of independent circles
            int num_indep_circles = independent_indices_iteration_len_ptr[indices_len_index];
            // if (blockIdx.x == DEBUG_BLOCK && threadIdx.x == 0) {
            //     printf("num_indep_circles = %d\n", num_indep_circles);
            // }
            // render independent circles
            deviceRenderIndependentCircles(independent_indices_iteration_ptr, num_indep_circles);
            // if (blockIdx.x == DEBUG_BLOCK && threadIdx.x == 0) {
            //     printf("rendered independent circles\n");
            // }
            // update segment_num_processed
            segment_num_processed += num_indep_circles;
            // update total_num_processed
            total_num_processed += num_indep_circles;
            // move to next independent group in the segment
            indices_len_index++;
            independent_indices_iteration_ptr += num_indep_circles;

            if (segment_num_processed >= SCAN_BLOCK_DIM ||
                total_num_processed >= numCircles) {
                break;
            }
        }
    }
}

int
circleInBoxConservativeHost(
    float circleX, float circleY, float circleRadius,
    float boxL, float boxR, float boxT, float boxB)
{

    // expand box by circle radius.  Test if circle center is in the
    // expanded box.

    if ( circleX >= (boxL - circleRadius) &&
         circleX <= (boxR + circleRadius) &&
         circleY >= (boxB - circleRadius) &&
         circleY <= (boxT + circleRadius) ) {
        return 1;
    } else {
        return 0;
    }
}

__device__
void renderPixelOneCircleDevice(int index) {
    float3 p = *(float3*)(&cuConstRendererParams.position[3*index]);
    float  rad = cuConstRendererParams.radius[index];

    float circleX = p.x;
    float circleY = p.y;
    float circleRadius = rad;

    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short blockImageWidth = blockDim.x;
    short blockImageHeight = blockDim.y;
    short blockImageCoordX = blockIdx.x * blockImageWidth;
    short blockImageCoordY = blockIdx.y * blockImageHeight;
    short blockImageBoxLeft = blockImageCoordX;
    short blockImageBoxRight = blockImageCoordX + blockImageWidth;
    short blockImageBoxBottom = blockImageCoordY;
    short blockImageBoxTop = blockImageCoordY + blockImageHeight;

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;
        
    if (circleInBox(circleX,
                    circleY,
                    circleRadius,
                    blockImageBoxLeft * invWidth,
                    blockImageBoxRight * invWidth,
                    blockImageBoxTop * invHeight,
                    blockImageBoxBottom * invHeight)) {
        short minX = static_cast<short>(imageWidth * (circleX - circleRadius));
        short maxX = static_cast<short>(imageWidth * (circleX + circleRadius)) + 1;
        short minY = static_cast<short>(imageHeight * (circleY - circleRadius));
        short maxY = static_cast<short>(imageHeight * (circleY + circleRadius)) + 1;

        short blockScreenMinX = (minX > blockImageBoxLeft)?((minX < blockImageBoxRight)?minX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMaxX = (maxX > blockImageBoxLeft)?((maxX < blockImageBoxRight)?maxX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMinY = (minY > blockImageBoxBottom)?((minY < blockImageBoxTop)?minY:blockImageBoxTop):blockImageBoxBottom;
        short blockScreenMaxY = (maxY > blockImageBoxBottom)?((maxY < blockImageBoxTop)?maxY:blockImageBoxTop):blockImageBoxBottom;

        int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
        int pixelY = blockIdx.y * blockDim.y + threadIdx.y;
        if (pixelX >= blockScreenMinX && pixelX < blockScreenMaxX && pixelY >= blockScreenMinY && pixelY < blockScreenMaxY) {
            float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + pixelX)]);
            float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                                 invHeight * (static_cast<float>(pixelY) + 0.5f));
            shadePixel(index, pixelCenterNorm, p, imgPtr);
        }
    }
}

#define BLOCK_IMAGE_WIDTH 16
#define BLOCK_IMAGE_HEIGHT 16
__device__
void renderPixelOneCircleSharedDevice(int index) {
    float3 p = *(float3*)(&cuConstRendererParams.position[3*index]);
    float  rad = cuConstRendererParams.radius[index];

    float circleX = p.x;
    float circleY = p.y;
    float circleRadius = rad;

    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short blockImageWidth = blockDim.x;
    short blockImageHeight = blockDim.y;
    short blockImageCoordX = blockIdx.x * blockImageWidth;
    short blockImageCoordY = blockIdx.y * blockImageHeight;
    short blockImageBoxLeft = blockImageCoordX;
    short blockImageBoxRight = blockImageCoordX + blockImageWidth;
    short blockImageBoxBottom = blockImageCoordY;
    short blockImageBoxTop = blockImageCoordY + blockImageHeight;

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    if (circleInBox(circleX,
                    circleY,
                    circleRadius,
                    blockImageBoxLeft * invWidth,
                    blockImageBoxRight * invWidth,
                    blockImageBoxTop * invHeight,
                    blockImageBoxBottom * invHeight)) {
        // Declare shared memory for the block's image data
        __shared__ float4 sharedBlockImage[BLOCK_IMAGE_WIDTH * BLOCK_IMAGE_HEIGHT];

        // Calculate block size in pixels
        int blockPixels = blockImageWidth * blockImageHeight;
        int threadId = threadIdx.y * blockDim.x + threadIdx.x;

        // Load block's image data into shared memory cooperatively
        for (int i = threadId; i < blockPixels; i += blockDim.x * blockDim.y) {
            int localX = i % blockImageWidth;
            int localY = i / blockImageWidth;
            int globalX = blockImageCoordX + localX;
            int globalY = blockImageCoordY + localY;

            // Bounds check for global image
            if (globalX < imageWidth && globalY < imageHeight) {
                int globalIndex = globalY * imageWidth + globalX;
                sharedBlockImage[i] = *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]);
            } else {
                // Initialize out-of-bounds pixels to transparent black
                sharedBlockImage[i] = make_float4(0.0f, 0.0f, 0.0f, 0.0f);
            }
        }

        // Synchronize to ensure all threads have loaded their data
        __syncthreads();

        short minX = static_cast<short>(imageWidth * (circleX - circleRadius));
        short maxX = static_cast<short>(imageWidth * (circleX + circleRadius)) + 1;
        short minY = static_cast<short>(imageHeight * (circleY - circleRadius));
        short maxY = static_cast<short>(imageHeight * (circleY + circleRadius)) + 1;

        short blockScreenMinX = (minX > blockImageBoxLeft)?((minX < blockImageBoxRight)?minX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMaxX = (maxX > blockImageBoxLeft)?((maxX < blockImageBoxRight)?maxX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMinY = (minY > blockImageBoxBottom)?((minY < blockImageBoxTop)?minY:blockImageBoxTop):blockImageBoxBottom;
        short blockScreenMaxY = (maxY > blockImageBoxBottom)?((maxY < blockImageBoxTop)?maxY:blockImageBoxTop):blockImageBoxBottom;

        int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
        int pixelY = blockIdx.y * blockDim.y + threadIdx.y;
        if (pixelX >= blockScreenMinX && pixelX < blockScreenMaxX && pixelY >= blockScreenMinY && pixelY < blockScreenMaxY) {
            // Calculate local coordinates within the block
            int localX = pixelX - blockImageCoordX;
            int localY = pixelY - blockImageCoordY;
            int localIndex = localY * blockImageWidth + localX;

            // Use shared memory pointer instead of global memory
            float4* imgPtr = &sharedBlockImage[localIndex];
            float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                                 invHeight * (static_cast<float>(pixelY) + 0.5f));
            shadePixel(index, pixelCenterNorm, p, imgPtr);
        }

        // Synchronize before writing back to global memory
        __syncthreads();

        // Write the modified shared memory data back to global memory
        for (int i = threadId; i < blockPixels; i += blockDim.x * blockDim.y) {
            int localX = i % blockImageWidth;
            int localY = i / blockImageWidth;
            int globalX = blockImageCoordX + localX;
            int globalY = blockImageCoordY + localY;

            // Bounds check for global image
            if (globalX < imageWidth && globalY < imageHeight) {
                int globalIndex = globalY * imageWidth + globalX;
                *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]) = sharedBlockImage[i];
            }
        }
    }
}

__global__
void renderPixelOneCircle(int index) {
    // renderPixelOneCircleDevice(index);
    renderPixelOneCircleSharedDevice(index);
}

__device__
void renderPixelOneCircleShared(int index,
                                float4 *sharedBlockImage,
                                float3 *sPosition,
                                float *sRadius,
                                float3 *sColor,
                                int offset) {
    // float3 p = *(float3*)(&cuConstRendererParams.position[3*index]);
    // float  rad = cuConstRendererParams.radius[index];
    float3 p = sPosition[index - offset];
    float  rad = sRadius[index - offset];

    float circleX = p.x;
    float circleY = p.y;
    float circleRadius = rad;

    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short blockImageWidth = blockDim.x;
    short blockImageHeight = blockDim.y;
    short blockImageCoordX = blockIdx.x * blockImageWidth;
    short blockImageCoordY = blockIdx.y * blockImageHeight;
    short blockImageBoxLeft = blockImageCoordX;
    short blockImageBoxRight = blockImageCoordX + blockImageWidth;
    short blockImageBoxBottom = blockImageCoordY;
    short blockImageBoxTop = blockImageCoordY + blockImageHeight;

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    if (circleInBoxConservative(circleX,
                    circleY,
                    circleRadius,
                    blockImageBoxLeft * invWidth,
                    blockImageBoxRight * invWidth,
                    blockImageBoxTop * invHeight,
                    blockImageBoxBottom * invHeight)) {
        short minX = static_cast<short>(imageWidth * (circleX - circleRadius));
        short maxX = static_cast<short>(imageWidth * (circleX + circleRadius)) + 1;
        short minY = static_cast<short>(imageHeight * (circleY - circleRadius));
        short maxY = static_cast<short>(imageHeight * (circleY + circleRadius)) + 1;

        short blockScreenMinX = (minX > blockImageBoxLeft)?((minX < blockImageBoxRight)?minX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMaxX = (maxX > blockImageBoxLeft)?((maxX < blockImageBoxRight)?maxX:blockImageBoxRight):blockImageBoxLeft;
        short blockScreenMinY = (minY > blockImageBoxBottom)?((minY < blockImageBoxTop)?minY:blockImageBoxTop):blockImageBoxBottom;
        short blockScreenMaxY = (maxY > blockImageBoxBottom)?((maxY < blockImageBoxTop)?maxY:blockImageBoxTop):blockImageBoxBottom;

        int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
        int pixelY = blockIdx.y * blockDim.y + threadIdx.y;
        if (pixelX >= blockScreenMinX && pixelX < blockScreenMaxX && pixelY >= blockScreenMinY && pixelY < blockScreenMaxY) {
            // Calculate local coordinates within the block
            int localX = pixelX - blockImageCoordX;
            int localY = pixelY - blockImageCoordY;
            int localIndex = localY * blockImageWidth + localX;

            // Use shared memory pointer instead of global memory
            float4* imgPtr = &sharedBlockImage[localIndex];
            float2 pixelCenterNorm = make_float2(invWidth * (static_cast<float>(pixelX) + 0.5f),
                                                 invHeight * (static_cast<float>(pixelY) + 0.5f));
            shadePixelNoGlobal(index,
                               pixelCenterNorm,
                               p,
                               imgPtr,
                               sRadius,
                               sColor,
                               offset);
        }
    }
}


__global__
void renderPixelIndependentCircles(int* independent_indices, int num_independent) {
    for (int i = 0; i < num_independent; i++)
        renderPixelOneCircleSharedDevice(independent_indices[i]);
        // renderPixelOneCircleDevice(independent_indices[i]);
}

__global__
void renderPixelAllCircles(int numCircles, int* independent_indices, int* independent_indices_len) {
    for (int i = 0; i < numCircles; i += SCAN_BLOCK_DIM) {
        int j = 0;
        int circles_so_far = 0;
        while (true) {
            int period_len = independent_indices_len[i + j];
            for (int k = 0; k < period_len; k++) {
                renderPixelOneCircleSharedDevice(independent_indices[i + circles_so_far + k]);
            }
            __syncthreads();
            j++;
            circles_so_far += period_len;
            if (circles_so_far >= SCAN_BLOCK_DIM ||
                i + circles_so_far >= numCircles) {
                break;
            }
        }
    }
}
__global__
void renderSharedPixelAllCircles(int numCircles, int* independent_indices, int* independent_indices_len) {
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short blockImageWidth = blockDim.x;
    short blockImageHeight = blockDim.y;
    short blockImageCoordX = blockIdx.x * blockImageWidth;
    short blockImageCoordY = blockIdx.y * blockImageHeight;

    // Declare shared memory for the block's image data
    __shared__ float4 sharedBlockImage[BLOCK_IMAGE_WIDTH * BLOCK_IMAGE_HEIGHT];

    // Calculate block size in pixels
    int blockPixels = blockImageWidth * blockImageHeight;
    int threadId = threadIdx.y * blockDim.x + threadIdx.x;

    // Load block's image data into shared memory cooperatively
    for (int i = threadId; i < blockPixels; i += blockDim.x * blockDim.y) {
        int localX = i % blockImageWidth;
        int localY = i / blockImageWidth;
        int globalX = blockImageCoordX + localX;
        int globalY = blockImageCoordY + localY;

        // Bounds check for global image
        if (globalX < imageWidth && globalY < imageHeight) {
            int globalIndex = globalY * imageWidth + globalX;
            sharedBlockImage[i] = *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]);
        } else {
            // Initialize out-of-bounds pixels to transparent black
            sharedBlockImage[i] = make_float4(0.0f, 0.0f, 0.0f, 0.0f);
        }
    }

    // Synchronize to ensure all threads have loaded their data
    __syncthreads();

    __shared__ float3 sPosition[SCAN_BLOCK_DIM];
    __shared__ float sRadius[SCAN_BLOCK_DIM];
    __shared__ float3 sColor[SCAN_BLOCK_DIM];
    __shared__ int shared_independent_indices[SCAN_BLOCK_DIM];
    __shared__ int shared_independent_indices_len[SCAN_BLOCK_DIM];
    for (int i = 0; i < numCircles; i += SCAN_BLOCK_DIM) {
        // load position and radius
        // for (int t = threadIdx.x + threadIdx.y * blockDim.x;
        //      t < SCAN_BLOCK_DIM && i + t < numCircles;
        //      t += blockDim.x * blockDim.y) {
        //     sPosition[t] = *(float3*)(&cuConstRendererParams.position[3*(i + t)]);
        //     sRadius[t] = cuConstRendererParams.radius[i + t];
        //     sColor[t] = *(float3*)(&cuConstRendererParams.color[3*(i + t)]);
        //     shared_independent_indices[t] = independent_indices[i + t];
        //     shared_independent_indices_len[t] = independent_indices_len[i + t];
        // }
        // 1. Load Positions (as a flat float array)
        // We need to load SCAN_BLOCK_DIM * 3 floats.
        // Each thread loads multiple floats until all position data is in shared memory.
        for (int t = threadIdx.x + threadIdx.y * blockDim.x;
             t < SCAN_BLOCK_DIM * 3 && i * 3 + t < 3 * numCircles; 
             t += blockDim.x * blockDim.y) {
            // Adjacent threads read adjacent floats from global memory. THIS IS COALESCED.
            ((float*)sPosition)[t] = ((float*)cuConstRendererParams.position)[i * 3 + t];
        }

        // 2. Load Radii
        // We need to load SCAN_BLOCK_DIM floats.
        for (int t = threadIdx.x + threadIdx.y * blockDim.x;
             t < SCAN_BLOCK_DIM && i + t < numCircles;
             t += blockDim.x * blockDim.y) {
            // Adjacent threads read adjacent floats. Also coalesced.
            sRadius[t] = cuConstRendererParams.radius[i + t];
        }
        
        // 3. Load Colors (as a flat float array)
        for (int t = threadIdx.x + threadIdx.y * blockDim.x;
             t < SCAN_BLOCK_DIM * 3 && i * 3 + t < 3 * numCircles; 
             t += blockDim.x * blockDim.y) {
            // Coalesced access for color data.
            ((float*)sColor)[t] = ((float*)cuConstRendererParams.color)[i * 3 + t];
        }

        // Load the other shared data (indices, etc.) in a similar coalesced fashion if needed.
        // For example:
        for (int t = threadIdx.x + threadIdx.y * blockDim.x;
             t < SCAN_BLOCK_DIM && i + t < numCircles;
             t += blockDim.x * blockDim.y) {
            shared_independent_indices[t] = independent_indices[i + t];
            shared_independent_indices_len[t] = independent_indices_len[i + t];
        }
        __syncthreads();
        int j = 0;
        int circles_so_far = 0;
        while (true) {
            // int period_len = independent_indices_len[i + j];
            int period_len = shared_independent_indices_len[j];
            for (int k = 0; k < period_len; k++) {
                // renderPixelOneCircleShared(independent_indices[i + circles_so_far + k],
                renderPixelOneCircleShared(shared_independent_indices[circles_so_far + k],
                                           sharedBlockImage,
                                           sPosition,
                                           sRadius,
                                           sColor,
                                           i);
            }
            __syncthreads();
            j++;
            circles_so_far += period_len;
            if (circles_so_far >= SCAN_BLOCK_DIM ||
                i + circles_so_far >= numCircles) {
                break;
            }
        }
    }

    // Synchronize before writing back to global memory
    __syncthreads();

    // Write the modified shared memory data back to global memory
    for (int i = threadId; i < blockPixels; i += blockDim.x * blockDim.y) {
        int localX = i % blockImageWidth;
        int localY = i / blockImageWidth;
        int globalX = blockImageCoordX + localX;
        int globalY = blockImageCoordY + localY;

        // Bounds check for global image
        if (globalX < imageWidth && globalY < imageHeight) {
            int globalIndex = globalY * imageWidth + globalX;
            *(float4*)(&cuConstRendererParams.imageData[4 * globalIndex]) = sharedBlockImage[i];
        }
    }
}
// epoch(one block analyze independence)
// period(a group of independent circles inside epoch)
// all epoch consists the total circles.
// one epoch consists of several periods
void renderIndependentCirclesByBlock(int numCircles, float* position, float* radius, Image* image) {
    dim3 blockDim(SCAN_BLOCK_DIM);
    dim3 gridDim((numCircles + SCAN_BLOCK_DIM - 1) / SCAN_BLOCK_DIM); 
    printf("gridDim = %d, blockDim = %d\n", gridDim.x, blockDim.x);
    // declare device vector
    thrust::device_vector<int> independent_indices(blockDim.x * gridDim.x);
    thrust::device_vector<int> independent_indices_len(blockDim.x * gridDim.x);
    // set zero
    thrust::fill(independent_indices.begin(), independent_indices.end(), 0);
    thrust::fill(independent_indices_len.begin(), independent_indices_len.end(), 0);
    cudaDeviceSynchronize();
    // launch kernel
    blockGatherIndependentCircles<<<gridDim, blockDim>>>(
        numCircles,
        thrust::raw_pointer_cast(independent_indices.data()),
        thrust::raw_pointer_cast(independent_indices_len.data())
    );
    cudaCheckError(cudaGetLastError());
    // cudaDeviceSynchronize();
    // copy to host
    thrust::host_vector<int> independent_indices_host(independent_indices);
    thrust::host_vector<int> independent_indices_len_host(independent_indices_len);
    // cudaCheckError(cudaGetLastError());
    // // print independent indices
    // printf("numCircles = %d--------------------------------\n", numCircles);
    // for (int i = 0; i < numCircles; i++) {
    //     printf("independent_indices[%d] = %d\n", i, independent_indices_host[i]);
    // }

    // first check whether all independent indices are correct
    // use host-side position and radius data directly (no need to copy from device constant memory)
    // Note: 'position' and 'radius' are member variables of CudaRenderer class containing host data
    // cudaCheckError(cudaGetLastError());
    // for (int i = 0; i < numCircles; i+=SCAN_BLOCK_DIM) {
    //     // one epoch
    //     int j = 0;
    //     int circles_so_far = 0;
    //     while (true) {
    //         int period_len = independent_indices_len_host[i + j];
    //         // check overlep of circles in the same period
    //         for (int k = 1; k < period_len; k++) {
    //             for (int u = 0; u < k; u++) {
    //                 int circle1 = independent_indices_host[i + circles_so_far + u];
    //                 int circle2 = independent_indices_host[i + circles_so_far + k];
    //                 float3 pos1 = *(float3*)(&position[circle1 * 3]);
    //                 float3 pos2 = *(float3*)(&position[circle2 * 3]);
    //                 float rad1 = radius[circle1];
    //                 float rad2 = radius[circle2];
    //                 if (circleInBoxConservativeHost(
    //                     pos1.x, pos1.y, rad1,
    //                     pos2.x - rad2, pos2.x + rad2, pos2.y + rad2, pos2.y - rad2
    //                 )) {
    //                     printf("Error: circle1 = %d, circle2 = %d\n", circle1, circle2);
    //                     exit(1);
    //                 }
    //             }
    //         }

    //         j++;
    //         circles_so_far += period_len;
    //         if (circles_so_far >= SCAN_BLOCK_DIM ||
    //             i + circles_so_far >= numCircles) {
    //             break;
    //         }
    //     }
    // }
    // now the index gather is verified
    int maxTileSize = 32 * 32; // 32x32 tile maximum (16KB)
    size_t sharedMemSize = maxTileSize * sizeof(float4);
    int* independent_indices_ptr = thrust::raw_pointer_cast(independent_indices.data());
    int* independent_indices_len_ptr = thrust::raw_pointer_cast(independent_indices_len.data());
#define ALL_CIRCLES 1
#if ALL_CIRCLES == 0
    for (int i = 0; i < numCircles; i+=SCAN_BLOCK_DIM) {
        // one epoch
        int j = 0;
        int circles_so_far = 0;
        while (true) {
            int period_len = independent_indices_len_host[i + j];

            dim3 blockDim(BLOCK_IMAGE_WIDTH, BLOCK_IMAGE_HEIGHT, 1);
            dim3 gridDim(
                (image->width + blockDim.x - 1) / blockDim.x,
                (image->height + blockDim.y - 1) / blockDim.y);
#define CIRCLE_PARALLEL 1
#if CIRCLE_PARALLEL == 1
            renderPixelIndependentCircles<<<gridDim, blockDim>>>(independent_indices_ptr + i + circles_so_far, period_len);
#else
            for (int k = 0; k < period_len; k++) {
                // printf("Start rendering circle %d\n", independent_indices_host[i + circles_so_far + k]);
                renderPixelOneCircle<<<gridDim, blockDim>>>(independent_indices_host[i + circles_so_far + k]);
            }
#endif

            j++;
            circles_so_far += period_len;
            if (circles_so_far >= SCAN_BLOCK_DIM ||
                i + circles_so_far >= numCircles) {
                break;
            }
        }
    }
#else
{
    dim3 blockDim(BLOCK_IMAGE_WIDTH, BLOCK_IMAGE_HEIGHT, 1);
    dim3 gridDim(
        (image->width + blockDim.x - 1) / blockDim.x,
        (image->height + blockDim.y - 1) / blockDim.y);
    // renderPixelAllCircles<<<gridDim, blockDim>>>(numCircles, independent_indices_ptr, independent_indices_len_ptr);
    renderSharedPixelAllCircles<<<gridDim, blockDim>>>(numCircles, independent_indices_ptr, independent_indices_len_ptr);
    cudaDeviceSynchronize();
    cudaCheckError(cudaGetLastError());
}
#endif
}

#define RENDER_VERSION 2
#if RENDER_VERSION == 0
void
CudaRenderer::render() {
    nvtxRangePush("CudaRenderer::render");

#define PIXEL_PARALLEL 0
#if PIXEL_PARALLEL
    thrust::device_vector<int> comparison_mask(numCircles, 1);
    pixelParallelRender(numCircles, image, comparison_mask);
    nvtxRangePop();
    return;
#else

    // Create a mask to skip certain circles during comparison
    // 1 means include in comparison, 0 means skip this circle
    nvtxRangePush("initialize_comparison_mask");
    thrust::device_vector<int> comparison_mask(numCircles, 1);
    nvtxRangePop();

    // Create persistent device vectors outside the loop to avoid repeated allocation/deallocation
    nvtxRangePush("initialize_persistent_vectors");
    thrust::device_vector<int> persistent_independent_flags(numCircles);
    thrust::device_vector<int> persistent_ex_vec(numCircles);
    thrust::device_vector<int> persistent_independent_indices; // Will be resized dynamically
    nvtxRangePop();

    // pixelParallelRender(numCircles, image, comparison_mask);
    // do while mask not all zero
    nvtxRangePush("render_loop");
    int num_independent = 0;
    while (num_independent < numCircles) {
    nvtxRangePush("render_independent_circles_outer");
#define BLOCK_KERNEL 1
#if BLOCK_KERNEL
        num_independent += render_independent_circles_in_block(numCircles, comparison_mask,
                                                              persistent_independent_flags,
                                                              persistent_ex_vec,
                                                              persistent_independent_indices);
#else
        num_independent += render_independent_circles(numCircles, comparison_mask);
#endif
    nvtxRangePop();
    }
    nvtxRangePop();

    nvtxRangePop(); // End of CudaRenderer::render
}
#endif
#elif RENDER_VERSION == 1
void
CudaRenderer::render() {
    nvtxRangePush("CudaRenderer::render");
    renderIndependentCirclesByBlock(numCircles, position, radius, image);
    nvtxRangePop();
}
#endif

/*

Algorithm:
1. binning kernel 
    1.1 split the screen to small buckets
    1.2 assign the overlapping circles to each bucket
    1.3 each bucket has a list of circles
2. render kernel
    2.1 each bucket renders the circles in the list

*/
__device__
void renderOneCircleInBucket(uint index,
                             short imageWidth,
                             short imageHeight,
                             short bucketWidth,
                             short bucketHeight,
                             short bucketX,
                             short bucketY,
                             short bucketMaxX,
                             short bucketMaxY) {
    int index3 = 3 * index;

    // read position and radius
    float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
    float  rad = cuConstRendererParams.radius[index];

    float invWidth = 1.f / imageWidth;
    float invHeight = 1.f / imageHeight;

    // Each thread renders exactly one pixel - the pixel assigned to this thread
    // The pixel coordinates are determined by the block and thread indices
    int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
    int pixelY = blockIdx.y * blockDim.y + threadIdx.y;

    // Check if this thread's pixel is within image bounds
    if (pixelX < imageWidth && pixelY < imageHeight) {
        float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * imageWidth + pixelX)]);
        float2 pixelCenterNorm = make_float2((static_cast<float>(pixelX) + 0.5f) * invWidth,
                                             (static_cast<float>(pixelY) + 0.5f) * invHeight);
        // shadePixel will check if the pixel is inside the circle and return early if not
        shadePixel(index, pixelCenterNorm, p, imgPtr);
    }
}
__device__
void binCirclesBlock(int offset) {
    // bucket setup
    const uint bucketSize = BLOCK_IMAGE_HEIGHT * BLOCK_IMAGE_WIDTH;
    uint numSharedBucketCircles = 0;
    __shared__ uint circles[bucketSize];
    __shared__ uint circleIndices[bucketSize];
    __shared__ uint scanOutput[bucketSize];
    __shared__ uint sScratch[bucketSize * 2];
    // set circles to 0
    for (int i = threadIdx.x + threadIdx.y * blockDim.x;
         i < bucketSize;
         i += blockDim.x * blockDim.y) {
        circles[i] = 0;
        circleIndices[i] = 0;
        scanOutput[i] = 0;
        sScratch[i] = 0;
        sScratch[i + bucketSize] = 0;
    }
    __syncthreads();
    
    // bucket screen
    short imageWidth = cuConstRendererParams.imageWidth;
    short imageHeight = cuConstRendererParams.imageHeight;
    short bucketWidth = blockDim.x;
    short bucketHeight = blockDim.y;
    short bucketX = blockIdx.x * bucketWidth;
    short bucketY = blockIdx.y * bucketHeight;
    short bucketMaxX = bucketX + bucketWidth;
    short bucketMaxY = bucketY + bucketHeight;

    // each thread check whether a circle in the bucket
    int circleIdx = threadIdx.x + threadIdx.y * blockDim.x;
    int index = circleIdx + offset;
    if (index < cuConstRendererParams.numCircles) {
        int index3 = 3 * index;
        float3 p = *(float3*)(&cuConstRendererParams.position[index3]);
        float  rad = cuConstRendererParams.radius[index];
        short minX = static_cast<short>(imageWidth * (p.x - rad));
        short maxX = static_cast<short>(imageWidth * (p.x + rad)) + 1;
        short minY = static_cast<short>(imageHeight * (p.y - rad));
        short maxY = static_cast<short>(imageHeight * (p.y + rad)) + 1;
        
        float invWidth = 1.f / imageWidth;
        float invHeight = 1.f / imageHeight;

        // check whether the circle is in the bucket
        if (circleInBoxConservative(p.x, p.y, rad,
            bucketX * invWidth, bucketMaxX * invWidth,
            bucketY * invHeight, bucketMaxY * invHeight)) {
            
            // set corresponding circles[circleIndex] = 1
            circles[circleIdx] = 1;
        }
    }
    // do exclusive scan to compute the indices
    __syncthreads();

    // // debug print the circles
    // if (threadIdx.x + threadIdx.y == 0) {
    //     if (blockIdx.x == 37 && blockIdx.y == 8) {
    //         for (int i = 0; i < bucketSize; i++) {
    //             printf("circles[%d] = %d\n", i, circles[i]);
    //         }
    //     }
    // }

    sharedMemExclusiveScan(circleIdx,
                           circles,
                           scanOutput,
                           sScratch,
                           bucketSize);
    __syncthreads();

    // get the number of circles in the bucket
    numSharedBucketCircles = scanOutput[bucketSize - 1] +
                             circles[bucketSize - 1];
    // gather the circle indices
    if (circles[circleIdx] == 1) {
        circleIndices[scanOutput[circleIdx]] = circleIdx;
    }
    
    __syncthreads();

    // // debug
    // if (threadIdx.x + threadIdx.y == 0) {
    //     if (numSharedBucketCircles > 0 && blockIdx.x == 12 && blockIdx.y == 12) {
    //         printf("DEBUG: Bucket[%d][%d] has %d circles\n", blockIdx.x, blockIdx.y, numSharedBucketCircles);
    //         for (int i = 0; i < numSharedBucketCircles; i++) {
    //             printf("  Circle %d (global index %d)\n", circleIndices[i], circleIndices[i] + offset);
    //         }
    //     }
    // }

    // render the circles in the bucket
    for (uint i = 0; i < numSharedBucketCircles; i++) {
        uint circleIdx = circleIndices[i];
        uint index = circleIdx + offset;
        // continue;
        renderOneCircleInBucket(index,
                                imageWidth,
                                imageHeight,
                                bucketWidth,
                                bucketHeight,
                                bucketX,
                                bucketY,
                                bucketMaxX,
                                bucketMaxY);
        __syncthreads();
    }

}

__global__
void binCirclesRender() {
    // STEP 1: Each thread maps to exactly one pixel
    int pixelX = blockIdx.x * blockDim.x + threadIdx.x;
    int pixelY = blockIdx.y * blockDim.y + threadIdx.y;

    // Early exit if pixel is out of bounds
    if (pixelX >= cuConstRendererParams.imageWidth || pixelY >= cuConstRendererParams.imageHeight) {
        return;
    }

    // Pre-compute pixel data
    float invWidth = 1.f / cuConstRendererParams.imageWidth;
    float invHeight = 1.f / cuConstRendererParams.imageHeight;
    float2 pixelCenterNorm = make_float2((static_cast<float>(pixelX) + 0.5f) * invWidth,
                                         (static_cast<float>(pixelY) + 0.5f) * invHeight);
    float4* imgPtr = (float4*)(&cuConstRendererParams.imageData[4 * (pixelY * cuConstRendererParams.imageWidth + pixelX)]);

    // STEP 2: Define bucket boundaries for this threadblock
    int bucketMinX = blockIdx.x * blockDim.x;
    int bucketMinY = blockIdx.y * blockDim.y;
    int bucketMaxX = bucketMinX + blockDim.x;
    int bucketMaxY = bucketMinY + blockDim.y;
    float bucketMinXNorm = bucketMinX * invWidth;
    float bucketMinYNorm = bucketMinY * invHeight;
    float bucketMaxXNorm = bucketMaxX * invWidth;
    float bucketMaxYNorm = bucketMaxY * invHeight;

    // STEP 3: Process circles in batches (shared memory constraint)
    const int BATCH_SIZE = BLOCK_IMAGE_WIDTH * BLOCK_IMAGE_HEIGHT; // 256
    __shared__ uint overlappingCircles[BATCH_SIZE];
    __shared__ uint scanResult[BATCH_SIZE];
    __shared__ uint scratchSpace[BATCH_SIZE * 2];
    __shared__ uint numOverlapping;

    int threadId = threadIdx.y * blockDim.x + threadIdx.x;

    // Process each batch of circles
    for (int batchStart = 0; batchStart < cuConstRendererParams.numCircles; batchStart += BATCH_SIZE) {

        // STEP 4: Initialize shared memory for this batch
        if (threadId < BATCH_SIZE) {
            overlappingCircles[threadId] = 0;
            scanResult[threadId] = 0;
            scratchSpace[threadId] = 0;
            scratchSpace[threadId + BATCH_SIZE] = 0;
        }
        if (threadId == 0) {
            numOverlapping = 0;
        }
        __syncthreads();

        // STEP 5: Each thread checks if one circle overlaps with this bucket
        int circleIdx = batchStart + threadId;
        if (circleIdx < cuConstRendererParams.numCircles && threadId < BATCH_SIZE) {
            int index3 = 3 * circleIdx;
            float3 circlePos = *(float3*)(&cuConstRendererParams.position[index3]);
            float circleRadius = cuConstRendererParams.radius[circleIdx];

            // Check if circle overlaps with bucket using conservative test
            if (circleInBoxConservative(circlePos.x, circlePos.y, circleRadius,
                                      bucketMinXNorm, bucketMaxXNorm,
                                      bucketMinYNorm, bucketMaxYNorm)) {
                overlappingCircles[threadId] = 1;
            }
        }
        __syncthreads();

        // STEP 6: Use exclusive scan to compact overlapping circle indices
        sharedMemExclusiveScan(threadId, overlappingCircles, scanResult, scratchSpace, BATCH_SIZE);
        __syncthreads();

        // STEP 7: Gather overlapping circle indices using scan results
        __shared__ uint overlappingIndices[BATCH_SIZE];
        if (threadId < BATCH_SIZE && overlappingCircles[threadId] == 1) {
            overlappingIndices[scanResult[threadId]] = threadId;
        }
        if (threadId == 0) {
            numOverlapping = scanResult[BATCH_SIZE - 1] + overlappingCircles[BATCH_SIZE - 1];
            // Debug output for the problematic bucket
            if (blockIdx.x == 12 && blockIdx.y == 24 && batchStart == 0) {
                printf("Bucket[%d][%d] batch %d: found %d overlapping circles\n",
                       blockIdx.x, blockIdx.y, batchStart, numOverlapping);
                printf("  Bucket bounds: [%d-%d, %d-%d]\n",
                       bucketMinX, bucketMaxX, bucketMinY, bucketMaxY);
                for (uint i = 0; i < numOverlapping; i++) {
                    printf("  Circle %d: local=%d, global=%d\n",
                           i, overlappingIndices[i], batchStart + overlappingIndices[i]);
                }
            }
        }
        __syncthreads();

        // STEP 8: Render only overlapping circles for this pixel
        for (uint i = 0; i < numOverlapping; i++) {
            uint localCircleIdx = overlappingIndices[i];
            uint globalCircleIdx = batchStart + localCircleIdx;

            if (globalCircleIdx < cuConstRendererParams.numCircles) {
                int index3 = 3 * globalCircleIdx;
                float3 circlePos = *(float3*)(&cuConstRendererParams.position[index3]);
                shadePixel(globalCircleIdx, pixelCenterNorm, circlePos, imgPtr);
            }
        }

        __syncthreads(); // Sync before next batch
    }
}

#if RENDER_VERSION == 2
void
CudaRenderer::render() {
    nvtxRangePush("CudaRenderer::render");
    dim3 blockDim(BLOCK_IMAGE_WIDTH, BLOCK_IMAGE_HEIGHT, 1);
    dim3 gridDim(
        (image->width + blockDim.x - 1) / blockDim.x,
        (image->height + blockDim.y - 1) / blockDim.y);
    // return;
    binCirclesRender<<<gridDim, blockDim>>>();
    cudaDeviceSynchronize();
    cudaCheckError(cudaGetLastError());
    nvtxRangePop();
}
#endif