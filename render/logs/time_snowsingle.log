Rendering to 1024x1024 image
Loaded data for 100000 circles from snow.par
Loaded scene with 100000 circles
---------------------------------------------------------
Initializing CUDA for <PERSON><PERSON><PERSON><PERSON><PERSON>
Found 8 CUDA devices
Device 0: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 1: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 2: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 3: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 4: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 5: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 6: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
Device 7: NVIDIA GeForce RTX 3090
   SMs:        82
   Global mem: 24260 MB
   CUDA Cap:   8.6
---------------------------------------------------------
WARNING: You're not running on a fast GPU, please consider using NVIDIA GTX 480, 670 or 780.
---------------------------------------------------------

Running benchmark, 4 frames, beginning at frame 0 ...
Dumping frames to logs/output_xxx.ppm
Copying image data from device
Wrote image file logs/output_0000.ppm
Copying image data from device
Wrote image file logs/output_0001.ppm
Copying image data from device
Wrote image file logs/output_0002.ppm
Copying image data from device
Wrote image file logs/output_0003.ppm
Clear:    0.0446 ms
Advance:  0.0010 ms
Render:   5.7763 ms
Total:    5.8219 ms
File IO:  73.2488 ms

Overall:  0.3163 sec (note units are seconds)
